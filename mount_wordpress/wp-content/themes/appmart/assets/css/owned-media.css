/* Error: Number calc(0.0714285714vw / 1px) isn't compatible with CSS calculations.
 *      ,
 * 1648 |     transform: scale(clamp(0.5, 100vw / 1400px, 1));
 *      |                                 ^^^^^^^^^^^^^^
 *      '
 *   mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss 1648:33  root stylesheet */

body::before {
  font-family: "Source Code Pro", "SF Mono", Monaco, Inconsolata, "Fira Mono",
      "Droid Sans Mono", monospace, monospace;
  white-space: pre;
  display: block;
  padding: 1em;
  margin-bottom: 1em;
  border-bottom: 2px solid black;
  content: "Error: Number calc(0.0714285714vw / 1px) isn't compatible with CSS calculations.\a      \2577 \a 1648 \2502      transform: scale(clamp(0.5, 100vw / 1400px, 1));\a      \2502                                  ^^^^^^^^^^^^^^\a      \2575 \a   mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss 1648:33  root stylesheet";
}
